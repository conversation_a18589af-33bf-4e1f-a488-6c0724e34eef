# AugmentCode-Free 核心逻辑分析文档

## 项目概述

AugmentCode-Free 是一个用于清理和重置 AugmentCode 插件相关数据的工具，通过伪装成新用户来无限使用 AugmentCode 的免费试用额度。该项目支持多种 IDE，包括 VS Code、Cursor、Windsurf 和 JetBrains 系列产品。

## 核心原理

### 基本思路
项目的核心思路是**通过清理本地存储的用户标识信息，让 AugmentCode 插件认为这是一个全新的用户**，从而重新获得免费试用额度。

### 主要清理目标
1. **数据库清理**：清理 IDE 本地数据库中包含 "augment" 关键字的条目
2. **遥测ID重置**：修改 machineId 和 devDeviceId 等设备标识
3. **SessionID修改**：针对 JetBrains 产品修改 SessionID

## 架构设计

### 模块结构
```
AugmentCode-Free/
├── main.py                    # 主入口，启动GUI
├── augment_tools_core/        # 核心功能模块
│   ├── cli.py                # 命令行接口
│   ├── database_manager.py   # 数据库清理
│   ├── telemetry_manager.py  # 遥测ID管理
│   ├── jetbrains_manager.py  # JetBrains SessionID管理
│   └── common_utils.py       # 通用工具函数
├── gui_qt6/                  # PyQt6图形界面
├── config_manager.py         # 配置管理
├── language_manager.py       # 多语言支持
└── languages/               # 语言文件
```

### 核心类和枚举
- `IDEType`: 支持的IDE类型枚举（VSCODE, CURSOR, WINDSURF, JETBRAINS）
- `ConfigManager`: 配置管理器，处理用户设置
- `LanguageManager`: 语言管理器，支持中英文切换

## 核心清理逻辑

### 1. 数据库清理 (database_manager.py)

**目标文件**: `state.vscdb` (SQLite数据库)
**清理逻辑**:
```python
def clean_vscode_database(db_path: Path, keyword: str = "augment") -> bool:
    # 1. 备份原数据库
    backup_path = create_backup(db_path)

    # 2. 连接SQLite数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 3. 查找包含关键字的条目
    query_select = "SELECT key FROM ItemTable WHERE key LIKE ?"
    like_pattern = f"%{keyword}%"
    cursor.execute(query_select, (like_pattern,))

    # 4. 删除匹配的条目
    query_delete = "DELETE FROM ItemTable WHERE key LIKE ?"
    cursor.execute(query_delete, (like_pattern,))
    conn.commit()
```

**关键点**:
- 清理 `ItemTable` 表中所有包含 "augment" 关键字的记录
- 自动备份原数据库，失败时可恢复
- 支持自定义关键字进行清理

### 2. 遥测ID重置 (telemetry_manager.py)

**目标文件**: `storage.json`
**清理逻辑**:
```python
def modify_vscode_telemetry_ids(storage_json_path: Path) -> bool:
    # 1. 生成新的ID
    new_machine_id = generate_new_machine_id()  # 64位十六进制
    new_device_id = generate_new_device_id()    # UUID v4

    # 2. 读取并修改JSON文件
    with open(storage_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 3. 更新关键字段
    data['machineId'] = new_machine_id
    data['telemetry']['machineId'] = new_machine_id
    data['telemetry']['devDeviceId'] = new_device_id

    # 4. 写回文件
    with open(storage_json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4)
```

**关键字段**:
- `machineId`: 机器标识符（根级和遥测级）
- `devDeviceId`: 设备标识符

### 3. JetBrains SessionID管理 (jetbrains_manager.py)

**目标文件**: `ide.general.xml`
**清理逻辑**:
```python
def modify_jetbrains_session_id(config_dir: Path, session_id: str) -> bool:
    # 1. 定位配置文件
    ide_general_file = config_dir / "options" / "ide.general.xml"

    # 2. 解析或创建XML结构
    if ide_general_file.exists():
        tree = ET.parse(ide_general_file)
        root = tree.getroot()
    else:
        root = ET.Element("application")
        component = ET.SubElement(root, "component", name="GeneralSettings")

    # 3. 更新SessionID属性
    session_property = component.find(".//property[@name='augment.session.id']")
    if session_property is not None:
        session_property.set("value", session_id)
    else:
        ET.SubElement(component, "property",
                     name="augment.session.id",
                     value=session_id)

    # 4. 保存XML文件
    tree.write(ide_general_file, encoding="utf-8", xml_declaration=True)
```

## 路径检测逻辑

### 跨平台路径支持
项目支持 Windows、macOS 和 Linux 三个平台，通过 `get_ide_paths()` 函数自动检测各IDE的配置路径：

**VS Code路径**:
- Windows: `%APPDATA%/Code/User/globalStorage/`
- macOS: `~/Library/Application Support/Code/User/globalStorage/`
- Linux: `~/.config/Code/User/globalStorage/`

**Cursor路径**:
- Windows: `%APPDATA%/Cursor/User/globalStorage/`
- macOS: `~/.cursor/globalStorage/`
- Linux: `~/.cursor/globalStorage/`

**Windsurf路径** (支持多种结构):
- 标准路径: `%APPDATA%/Windsurf/` 或 `~/.config/Windsurf/`
- Codeium路径: `~/.codeium/windsurf/`

**JetBrains路径**:
- Windows: `%APPDATA%/JetBrains/`
- macOS: `~/Library/Application Support/JetBrains/`
- Linux: `~/.config/JetBrains/`

### 智能路径检测
对于 Windsurf，项目实现了智能路径检测机制：
```python
def detect_windsurf_paths() -> Dict[str, Path]:
    # 按优先级检查多个可能的路径
    possible_base_dirs = [
        standard_base,           # 标准VSCode结构
        home / ".codeium" / "windsurf",  # Codeium结构
        home / ".windsurf",
    ]

    # 检查不同的子目录结构
    possible_structures = [
        ("User/globalStorage", "extensions"),
        ("globalStorage", "extensions"),
        ("data/User/globalStorage", "data/extensions"),
    ]
```

## 用户界面设计

### GUI架构 (PyQt6)
- **MainWindow**: 主窗口容器
- **WelcomePage**: 欢迎页面（首次运行）
- **MainPage**: 主功能页面
- **AboutDialog**: 关于对话框

### 主要功能按钮
1. **一键修改所有配置**: 执行完整的清理流程
2. **关闭选中的IDE**: 自动检测并关闭正在运行的IDE进程
3. **清理IDE数据库**: 单独执行数据库清理
4. **修改IDE遥测ID**: 单独执行ID重置

### 安全机制
- **进程检测**: 自动检测IDE是否在运行，避免文件冲突
- **数据备份**: 所有操作前自动备份原文件
- **用户确认**: 重要操作前显示确认对话框
- **错误恢复**: 操作失败时自动从备份恢复

## 多语言支持

项目支持中英文双语，通过 JSON 配置文件实现：
- `languages/zh_CN.json`: 中文语言包
- `languages/en_US.json`: 英文语言包

语言切换通过 `LanguageManager` 类管理，支持运行时动态切换。

## 配置管理

通过 `ConfigManager` 类管理用户配置：
- 语言偏好
- 窗口几何信息
- 上次选择的IDE
- 首次运行标记

配置文件存储在 `config/settings.json`。

## 工作流程详解

### 完整清理流程 ("一键修改所有配置")
1. **进程检测**: 检查目标IDE是否正在运行
2. **路径检测**: 自动检测IDE配置文件路径
3. **数据备份**: 备份 `state.vscdb` 和 `storage.json`
4. **数据库清理**: 删除包含 "augment" 的数据库记录
5. **ID重置**: 生成并更新新的遥测ID
6. **SessionID更新**: (仅JetBrains) 更新XML配置中的SessionID
7. **验证完成**: 确认所有操作成功完成

### 错误处理机制
- **文件锁定**: 如果IDE正在运行，提示用户先关闭
- **权限不足**: 检查文件访问权限，提供解决建议
- **备份失败**: 如果无法创建备份，中止操作
- **操作失败**: 自动从备份恢复原始文件

## 技术特点

### 1. 跨平台兼容性
- 使用 `pathlib.Path` 处理路径，确保跨平台兼容
- 针对不同操作系统的特定路径结构进行适配
- 支持 Windows、macOS、Linux 三大平台

### 2. 安全性设计
- 所有修改操作前自动创建备份
- 失败时自动恢复机制
- 用户确认对话框防止误操作
- 进程检测避免文件冲突

### 3. 用户体验
- 现代化的 PyQt6 图形界面
- 多语言支持（中英文）
- 详细的操作日志和状态反馈
- 智能的IDE检测和路径发现

### 4. 扩展性
- 模块化设计，易于添加新的IDE支持
- 配置驱动的路径检测
- 插件化的清理逻辑

## 使用场景和限制

### 适用场景
- AugmentCode 免费试用额度用完后重置
- 多账号切换使用
- 开发环境清理和重置

### 使用限制
- 需要完全关闭目标IDE才能执行清理
- 会清除所有 AugmentCode 相关的聊天记录和设置
- 不保证在所有 AugmentCode 版本更新后仍然有效

### 风险提示
- 操作会删除本地的 AugmentCode 数据
- 重要的聊天记录需要提前备份
- 可能违反 AugmentCode 的服务条款

## 总结

AugmentCode-Free 的核心逻辑是通过系统性地清理 IDE 中存储的 AugmentCode 相关标识信息，包括数据库记录、遥测ID和SessionID，使 AugmentCode 插件无法识别用户的历史使用记录，从而重新获得免费试用资格。

该工具设计精巧，支持多平台、多IDE，具有完善的安全机制和用户友好的图形界面，是一个功能完整的IDE维护工具。其核心价值在于：

1. **技术实现精良**: 跨平台路径检测、智能备份恢复、进程管理等
2. **用户体验优秀**: 图形界面、多语言、详细反馈
3. **安全机制完善**: 备份恢复、确认对话框、错误处理
4. **架构设计合理**: 模块化、可扩展、易维护

这是一个展示了如何通过技术手段绕过软件使用限制的典型案例，同时也体现了优秀的软件工程实践。